import { Router } from "express";
import { checkIsAdmin, checkLogin } from "../middleware/auth.js";
import {
	createEvent,
	deleteEvent,
	getAllEvents,
	getSingleEvent,
	markAttendee,
	markNonAttendee,
	updateEvent,
} from "../controllers/event.controller.js";
import upload from "../middleware/eventUpload.js";

const router = Router();

router.post("/", checkLogin, checkIsAdmin, createEvent);

router.get("/", checkLogin, getAllEvents);

router.get("/:id", checkLogin, getSingleEvent);

router.put(
	"/:id",
	checkLogin,
	checkIsAdmin,
	upload.array("images", 10),
	updateEvent
);

router.delete("/:id", checkLogin, checkIsAdmin, deleteEvent);
router.patch("/attend/:id", checkLogin, markAttendee);
router.patch("/not-attend/:id", checkLogin, markNonAttendee);

export default router;
