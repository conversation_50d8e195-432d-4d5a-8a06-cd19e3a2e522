import { Card, Image, Text, Box, Flex, Stack } from "@mantine/core";
import type { Event } from "../../types";
import { Calendar, Clock } from "lucide-react";

type EventCardProps = {
	event: Event;
};

export const EventCard: React.FC<EventCardProps> = ({ event }) => {
	const formatDate = (dateInput: string | Date) => {
		const date =
			typeof dateInput === "string" ? new Date(dateInput) : dateInput;
		return date.toLocaleDateString("en-US", {
			month: "short",
			day: "numeric",
			year: "numeric",
		});
	};

	const formatTime = (dateInput: string | Date) => {
		const date =
			typeof dateInput === "string" ? new Date(dateInput) : dateInput;
		return date.toLocaleTimeString("en-US", {
			hour: "numeric",
			minute: "2-digit",
			hour12: true,
		});
	};

	return (
		<Card
			shadow="xl"
			p={0}
			radius="md"
			withBorder
			style={{
				overflow: "hidden",
				boxShadow: "var(--shadow-card)",
				flex: 1,
			}}
		>
			<Box>
				<Image
					src={
						"https://media.istockphoto.com/id/978975308/vector/upcoming-events-neon-signs-vector-upcoming-events-design-template-neon-sign-light-banner-neon.jpg?s=612x612&w=0&k=20&c=VMCoJJda9L17HVkFOFB3fyDpjC4Qu2AsyYn3u4T4F4c="
					}
					alt={event.name}
					fit="cover"
				/>
			</Box>

			<Stack p="md">
				<Stack mb="sm">
					<Text fw={700} size="lg">
						{event.name}
					</Text>
					<Text c="dimmed" size="sm" mt={4}>
						{event.description}
					</Text>
				</Stack>

				<Box pt="sm" style={{ borderTop: "1px solid rgba(0,0,0,0.1)" }}>
					<Flex align="center" gap="xs" mb={4}>
						<Calendar size={16} color="#228be6" />
						<Text size="sm" fw={500}>
							{formatDate(event.startDate)} -{" "}
							{formatDate(event.endDate)}
						</Text>
					</Flex>

					<Flex align="center" gap="xs">
						<Clock size={16} color="#228be6" />
						<Text size="sm">
							{formatTime(event.startDate)} -{" "}
							{formatTime(event.endDate)}
						</Text>
					</Flex>
				</Box>
			</Stack>
		</Card>
	);
};
