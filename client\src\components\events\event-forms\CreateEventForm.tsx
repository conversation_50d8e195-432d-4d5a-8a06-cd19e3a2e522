import {
	<PERSON><PERSON>,
	Con<PERSON>er,
	<PERSON><PERSON>,
	Textarea,
	TextInput,
	Title,
	Text,
	Card,
	Flex,
	Group,
	Image,
	SimpleGrid,
	ActionIcon,
	useMantineTheme,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { DateTimePicker } from "@mantine/dates";
import type React from "react";
import apiClient from "../../../config/axios";
import { notifications } from "@mantine/notifications";
import { IconCheck, IconX } from "@tabler/icons-react";
import { isAxiosError } from "axios";
import { useRef, useState } from "react";
import {
	CalendarDays,
	FileText,
	Image as ImageIcon,
	Save,
	Upload,
} from "lucide-react";
import { Dropzone } from "@mantine/dropzone";
import { useHover } from "@mantine/hooks";
import { GalleryViewer } from "../../GalleryViewer";
import { motion, AnimatePresence } from "framer-motion";

type EventFormValues = {
	name: string;
	description: string;
	startDate: Date | null;
	endDate: Date | null;
};

type ImageFile = {
	id: string;
	file: File;
	preview: string;
};

type EventFormProps = {
	initialValues?: Partial<EventFormValues>;
	onSubmit?: (values: EventFormValues) => void;
};

export const CreateEventForm: React.FC = (props: EventFormProps) => {
	const [loading, setLoading] = useState<boolean>(false);
	const [images, setImages] = useState<ImageFile[]>([]);
	// const [thumbnailId, setThumbnailId] = useState<string | null>(null);
	const theme = useMantineTheme();

	const fieldRefs = useRef<Record<string, HTMLElement | null>>({});

	const setFieldRef = (field: string) => (el: HTMLElement | null) => {
		fieldRefs.current[field] = el;
	};

	const scrollToError = (field: string) => {
		const ref = fieldRefs.current[field];
		if (ref) {
			ref.scrollIntoView({ behavior: "smooth", block: "center" });
			if ("focus" in ref)
				(ref as HTMLElement).focus({ preventScroll: true });
		}
	};

	const handleDrop = (files: File[]) => {
		setImages(prev => {
			const existingKeys = new Set(
				prev.map(
					img =>
						`${img.file.name}-${img.file.size}-${img.file.lastModified}`
				)
			);

			let haveDuplicateImages = false;
			const newFiles = files.filter(file => {
				const fileKey = `${file.name}-${file.size}-${file.lastModified}`;
				if (existingKeys.has(fileKey)) {
					haveDuplicateImages = true;
					return false;
				}
				return true;
			});

			if (haveDuplicateImages) {
				notifications.show({
					title: "Duplicate Images",
					message: "Duplicate images are not allowed.",
					color: "red",
					icon: <IconX />,
				});
			}

			const newImages = newFiles.map(file => ({
				id: crypto.randomUUID(),
				file,
				preview: URL.createObjectURL(file),
			}));

			return [...prev, ...newImages];
		});
	};

	const handleReject = () => {
		notifications.show({
			title: "Invalid File",
			message: "Only image files are allowed.",
			color: "red",
			icon: <IconX />,
		});
	};

	const removeImage = (id: string) => {
		setImages(prev => prev.filter(img => img.id !== id));
	};

	const handleSubmit = async (values: EventFormValues) => {
		const validation = form.validate();
		if (validation.hasErrors) {
			const firstError = Object.keys(validation.errors)[0];
			scrollToError(firstError);
			return;
		}

		setLoading(true);
		if (!values.startDate || !values.endDate) {
			throw new Error("Dates are required");
		}
		const formData = new FormData();
		formData.append("name", values.name);
		formData.append("description", values.description);
		formData.append("startDate", new Date(values.startDate).toISOString());
		formData.append("endDate", new Date(values.endDate).toISOString());

		images.forEach(img => {
			formData.append("images", img.file);
		});
		try {
			const res = await apiClient.post("/api/events", formData, {
				headers: {
					"Content-Type": "multipart/form-data",
				},
			});
			notifications.show({
				title: "Success",
				message: res.data.message || "Event created successfully",
				color: "green",
				icon: <IconCheck />,
			});
			form.reset();
			setImages([]);
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Unable to create event",
					message:
						err.response?.data?.message || "Can't create event",
					color: "red",
					icon: <IconX />,
				});
			}
		} finally {
			setLoading(false);
		}
	};

	const form = useForm<EventFormValues>({
		initialValues: {
			name: props.initialValues?.name || "",
			description: props.initialValues?.description || "",
			startDate: props.initialValues?.startDate || null,
			endDate: props.initialValues?.endDate || null,
		},
		validate: {
			name: val => (val.trim() === "" ? "Name is required" : null),
			description: val =>
				val.trim() === "" ? "Description is required" : null,
			startDate: (value, values) => {
				if (!value) return "Start date is required";

				const dateValue =
					value instanceof Date ? value : new Date(value);

				const now = new Date();

				if (dateValue.getTime() < now.getTime()) {
					return "Start date cannot be in the past";
				}

				if (
					values.startDate &&
					values.endDate &&
					values.startDate >= values.endDate
				) {
					return "Start date must be before End Date";
				}

				return null;
			},
			endDate: (value, values) =>
				!value
					? "End date is required"
					: values.startDate && value < values.startDate
						? "End date cannot be before start date"
						: null,
		},
		transformValues: values => ({
			...values,
			name: values.name.trim(),
			description: values.description.trim(),
		}),
	});

	return (
		<Container>
			<Stack mb="md" gap={0}>
				<Title>Create New Event</Title>
				<Text c="dimmed">
					Fill out the form below to create a new event with images
					and scheduling details.
				</Text>
			</Stack>

			<form
				onSubmit={e => {
					e.preventDefault();
					handleSubmit(form.values);
				}}
			>
				<Stack>
					<Card shadow="xl" p="lg" withBorder>
						<Stack>
							<Flex align="center" gap="xs">
								<FileText size={20} />
								<Title order={3}>Basic Information</Title>
							</Flex>
							<TextInput
								ref={setFieldRef("name")}
								label="Event Name"
								placeholder="Enter event name"
								{...form.getInputProps("name")}
								withAsterisk
								error={
									form.errors["name"]
										? form.errors["name"]
										: undefined
								}
							/>

							<Textarea
								ref={setFieldRef("description")}
								label="Description"
								placeholder="Enter event description"
								{...form.getInputProps("description")}
								withAsterisk
								error={
									form.errors["description"]
										? form.errors["description"]
										: undefined
								}
							/>
						</Stack>
					</Card>

					<Card shadow="xl" p="lg" withBorder>
						<Stack>
							<Flex align="center" gap="xs">
								<CalendarDays size={20} />
								<Title order={3}>Schedule</Title>
							</Flex>
							<Flex
								align="center"
								justify="space-between"
								gap="md"
							>
								<DateTimePicker
									ref={setFieldRef("startDate")}
									style={{ flex: 1 }}
									label="Start Date and Time"
									placeholder="Pick start date"
									{...form.getInputProps("startDate")}
									withAsterisk
									hideOutsideDates={true}
									weekendDays={[]}
									timePickerProps={{
										withDropdown: true,
										format: "24h",
									}}
								/>

								<DateTimePicker
									ref={setFieldRef("endDate")}
									style={{ flex: 1 }}
									label="End Date and Time"
									placeholder="Pick end date"
									{...form.getInputProps("endDate")}
									withAsterisk
									hideOutsideDates={true}
									weekendDays={[]}
									timePickerProps={{
										withDropdown: true,
										format: "24h",
									}}
								/>
							</Flex>
						</Stack>
					</Card>

					<Card shadow="lg" p="lg" withBorder>
						<Stack>
							<Flex align="center" gap="xs">
								<ImageIcon size={20} />
								<Title order={3}>Images</Title>
							</Flex>

							<Dropzone
								onDrop={handleDrop}
								onReject={handleReject}
								accept={{
									"image/*": [],
								}}
								multiple
							>
								<Group
									justify="center"
									gap="xl"
									mih={220}
									style={{ pointerEvents: "none" }}
								>
									<Stack align="center" gap="xs">
										<Text size="xl" inline>
											Drop Images here or click to upload
											images
										</Text>
										<Text
											size="sm"
											c="dimmed"
											inline
											mt={7}
										>
											Attach as many images as you like
										</Text>
										<Button
											mt="sm"
											style={{
												color: theme.colors.gray[7],
												backgroundColor:
													theme.colors.gray[1],
												height: "45px",
												width: "180px",
												borderRadius: "6px",
											}}
											styles={{
												root: {
													padding: "12px 24px",
												},
											}}
											leftSection={
												<ImageIcon size={16} />
											}
										>
											Choose Images
										</Button>
									</Stack>
								</Group>
							</Dropzone>

							{images.length > 0 && (
								<Stack mt="md">
									<Text fw={500}>
										Selected Images ({images.length})
									</Text>
									<ImageGrid
										images={images}
										removeImage={removeImage}
										setImages={setImages}
									/>
								</Stack>
							)}
						</Stack>
					</Card>

					<Flex justify="flex-end">
						<Button
							size="sm"
							loading={loading}
							type="submit"
							leftSection={<Save size={16} />}
						>
							Create Event
						</Button>
					</Flex>
				</Stack>
			</form>
		</Container>
	);
};

type ImageGridProps = {
	images: ImageFile[];
	removeImage: (id: string) => void;
	setImages: React.Dispatch<React.SetStateAction<ImageFile[]>>;
};

const ImageCard: React.FC<{
	img: ImageFile;
	removeImage: (id: string) => void;
	setImages: React.Dispatch<React.SetStateAction<ImageFile[]>>;
	onPreview: () => void;
	imageIndex: number;
}> = ({ img, removeImage, setImages, onPreview, imageIndex }) => {
	const { hovered, ref } = useHover();
	const replaceInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>(
		{}
	);
	const replaceImage = (id: string, newFile: File) => {
		setImages(prev => {
			const duplicate = prev.find(
				img => img.file.name === newFile.name && img.id !== id
			);
			if (duplicate) {
				notifications.show({
					title: "Already selected",
					message: "Same image can't be selected again",
					color: "red",
					icon: <IconX />,
				});
				return prev;
			}

			const imageIndex = prev.findIndex(img => img.id === id);
			if (imageIndex === -1) return prev;

			URL.revokeObjectURL(prev[imageIndex].preview);

			const updatedImage: ImageFile = {
				id,
				file: newFile,
				preview: URL.createObjectURL(newFile),
			};

			const updated = [...prev];
			updated[imageIndex] = updatedImage;
			return updated;
		});
	};

	const handleReplaceClick = (id: string) => {
		replaceInputRefs.current[id]?.click();
	};

	const handleReplaceFile = (
		e: React.ChangeEvent<HTMLInputElement>,
		id: string
	) => {
		const file = e.target.files?.[0];
		if (file && file.type.startsWith("image/")) {
			replaceImage(id, file);
		}
		e.target.value = "";
	};
	return (
		<motion.div
			ref={ref}
			layout
			initial={{ opacity: 0, scale: 1 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.9 }}
			transition={{ duration: 0.3, delay: imageIndex * 0.1 }}
			style={{ display: "inline-block", margin: "10px" }}
		>
			<Card
				key={img.id}
				shadow={hovered ? "lg" : "sm"}
				radius="md"
				p={0}
				withBorder
				style={{
					width: "200px",
				}}
			>
				<Group>
					<Image
						src={img.preview}
						alt="Preview"
						style={{
							objectFit: "cover",
							width: "250px",
							height: "200px",
							filter: hovered
								? "brightness(90%)"
								: "brightness(100%)",
							cursor: "pointer",
						}}
						onClick={onPreview}
					/>

					<ActionIcon
						color="red"
						variant="filled"
						radius="xl"
						size="md"
						onClick={() => removeImage(img.id)}
						style={{
							position: "absolute",
							top: 8,
							right: 8,
							opacity: hovered ? 1 : 0,
							transition: "opacity 0.2s",
							pointerEvents: hovered ? "auto" : "none",
						}}
					>
						<IconX size={18} />
					</ActionIcon>
					<ActionIcon
						color="blue"
						variant="filled"
						radius="xl"
						size="md"
						onClick={() => handleReplaceClick(img.id)}
						style={{
							position: "absolute",
							top: 40,
							right: 8,
							opacity: hovered ? 1 : 0,
							transition: "opacity 0.2s",
							pointerEvents: hovered ? "auto" : "none",
						}}
					>
						<Upload size={18} />
					</ActionIcon>
					<input
						type="file"
						accept="image/*"
						className="hidden"
						ref={el => {
							replaceInputRefs.current[img.id] = el;
						}}
						onChange={e => handleReplaceFile(e, img.id)}
					/>
				</Group>
			</Card>
		</motion.div>
	);
};

const ImageGrid: React.FC<ImageGridProps> = ({
	images,
	removeImage,
	setImages,
}) => {
	const [viewerOpened, setViewerOpened] = useState<boolean>(false);
	const [currentIdx, setCurrentIdx] = useState<number>(0);
	return (
		<>
			<SimpleGrid cols={{ base: 2, sm: 3, lg: 5 }} spacing="md">
				<AnimatePresence>
					{images.map((img, index) => (
						<ImageCard
							key={img.id}
							img={img}
							removeImage={removeImage}
							setImages={setImages}
							onPreview={() => {
								setCurrentIdx(index);
								setViewerOpened(true);
							}}
							imageIndex={index}
						/>
					))}
				</AnimatePresence>
			</SimpleGrid>

			<GalleryViewer
				opened={viewerOpened}
				onClose={() => setViewerOpened(false)}
				images={images.map(img => img.preview)}
				initial={currentIdx}
			/>
		</>
	);
};
