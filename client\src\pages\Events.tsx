import { Button, Container, Group, Tabs, Title } from "@mantine/core";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import apiClient from "../config/axios";
import type { Event, EventsApiResponse } from "../types";
import FullScreenLoader from "../components/FullScreenLoader";
import { ListPlus } from "lucide-react";
import { PastEvents, UpcomingEvents } from "../components/events";

const Events = () => {
	const navigate = useNavigate();
	const [pastEvents, setPastEvents] = useState<Event[]>([]);
	const [upcomingEvents, setUpcomingEvents] = useState<Event[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const fetchData = async () => {
		try {
			setLoading(true);
			const res = await apiClient.get<EventsApiResponse>("/api/events");
			setPastEvents(res.data.pastEvents);
			setUpcomingEvents(res.data.upcomingEvents);
		} catch (err) {
			console.log(err);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchData();
	}, []);

	if (loading || !pastEvents || !upcomingEvents) {
		return <FullScreenLoader />;
	}
	return (
		<Container p={0}>
			<Group justify="space-between">
				<Title order={1}>Events</Title>
				<Button
					leftSection={<ListPlus size={16} />}
					onClick={() => navigate("/events/create-event")}
				>
					Create Event
				</Button>
			</Group>

			<Tabs p={0} mt="md" defaultValue="past-events">
				<Tabs.List>
					<Tabs.Tab value="past-events">Past Events</Tabs.Tab>
					<Tabs.Tab value="upcoming-events">Upcoming Events</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="past-events">
					<PastEvents pastEvents={pastEvents} />
				</Tabs.Panel>

				<Tabs.Panel value="upcoming-events">
					<UpcomingEvents upcomingEvents={upcomingEvents} />
				</Tabs.Panel>
			</Tabs>
		</Container>
	);
};

export default Events;
