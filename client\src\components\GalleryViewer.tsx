import React, { useEffect, useState } from "react";
import { Modal, Image, ActionIcon, Box, rem } from "@mantine/core";
import { createStyles } from "@mantine/styles";
import { IconChevronLeft, IconChevronRight } from "@tabler/icons-react";

interface GalleryViewerProps {
	opened: boolean;
	onClose: () => void;
	images: string[];
	initial?: number;
}

const useStyles = createStyles(() => ({
	view: {
		position: "relative",
		margin: 0,
		padding: 0,
	},

	arrowWrap: {
		position: "fixed",
		top: "50%",
		left: 0,
		right: 0,
		display: "flex",
		justifyContent: "space-between",
		alignItems: "center",
		transform: "translateY(-50%)",
		zIndex: 1050,
	},

	arrowBtn: {
		pointerEvents: "auto",
		color: "#fff",
		background: "rgba(0,0,0,.55)",
		backdropFilter: "blur(2px)",
		transition: "background 120ms, opacity 150ms",
		opacity: 1,
		boxShadow: "0 2px 8px rgba(0,0,0,.45)", // ▼ shadow on btn
		"&:hover": { background: "rgba(0,0,0,.75)" },
	},

	visible: { opacity: 1 },

	imgShadow: {
		borderRadius: rem(8),
		boxShadow: "0 3px 12px rgba(0,0,0,.45)", // ▼ shadow on img
	},
}));
/* ──────────────────────────────────────────────────── */

export const GalleryViewer: React.FC<GalleryViewerProps> = ({
	opened,
	onClose,
	images,
	initial = 0,
}) => {
	const { classes, cx } = useStyles();
	const [idx, setIdx] = useState(initial);
	const [hovered, setHovered] = useState(false); // ← controls arrow opacity

	useEffect(() => {
		if (opened) setIdx(initial);
	}, [opened, initial]);

	if (images.length === 0) return null;

	const next = () => setIdx(i => Math.min(i + 1, images.length - 1));
	const prev = () => setIdx(i => Math.max(i - 1, 0));

	return (
		<Modal
			opened={opened}
			onClose={onClose}
			centered
			padding={0}
			withCloseButton={false}
			size="auto"
			overlayProps={{ blur: 2 }}
			onMouseEnter={() => setHovered(true)}
			onMouseLeave={() => setHovered(false)}
			styles={{
				content: {
					padding: 0,
					margin: 0,
					background: "transparent",
					border: 0,
					boxShadow: "none",
				},
				body: { padding: 0 },
			}}
		>
			<Box className={cx(classes.view)}>
				<Image
					className={cx(classes.imgShadow)}
					src={images[idx]}
					alt={`gallery-img-${idx}`}
					fit="contain"
					style={{
						borderRadius: "8px",
					}}
					h="50vh"
					w="65vw"
				/>
			</Box>
			{images.length > 1 && (
				<Box className={cx(classes.arrowWrap)}>
					<ActionIcon
						className={cx(classes.arrowBtn)}
						variant="filled"
						radius="xl"
						color="dark"
						size="lg"
						onClick={prev}
						disabled={idx === 0}
						style={{ opacity: 1 }} // always visible
					>
						<IconChevronLeft size={20} />
					</ActionIcon>

					<ActionIcon
						className={cx(classes.arrowBtn)}
						variant="filled"
						radius="xl"
						size="lg"
						color="dark"
						onClick={next}
						disabled={idx === images.length - 1}
						style={{ opacity: 1 }} // always visible
					>
						<IconChevronRight size={20} />
					</ActionIcon>
				</Box>
			)}
		</Modal>
	);
};
