{"name": "client", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "test": "echo \"Error: no test specified\" && exit 1", "precommit": "lint-staged"}, "dependencies": {"@mantine/core": "^8.1.3", "@mantine/dates": "^8.1.3", "@mantine/dropzone": "^8.1.3", "@mantine/form": "^8.1.3", "@mantine/hooks": "^8.1.3", "@mantine/modals": "^8.1.3", "@mantine/notifications": "^8.1.3", "@mantine/styles": "^6.0.22", "@tabler/icons-react": "^3.34.0", "@tailwindcss/vite": "^4.1.11", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "deep-diff": "^1.0.2", "framer-motion": "^12.23.12", "i": "^0.3.7", "lucide-react": "^0.525.0", "npm": "^11.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-phone-input-2": "^2.15.1", "react-router-dom": "^7.6.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "validator": "^13.15.15"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/deep-diff": "^1.0.5", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/validator": "^13.15.2", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "lint-staged": "^16.1.4", "prettier": "^3.6.2", "tw-animate-css": "^1.3.5", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.0.3"}, "description": "", "main": "index.js", "keywords": [], "author": "", "license": "ISC", "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write", "prettier --check"], "*.{json,css,md}": ["prettier --write", "prettier --check"]}}