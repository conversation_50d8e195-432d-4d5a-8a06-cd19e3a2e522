import { Box, Flex, Title, Grid, Paper, Text } from "@mantine/core";
import type { Event } from "../../types";
import { EventCard } from "./EventCard";

type UpcomingEventsProps = {
	upcomingEvents: Event[];
};

export const UpcomingEvents: React.FC<UpcomingEventsProps> = props => {
	return (
		<Box mt="lg" p={0} component="section">
			<Flex align="center" gap="sm" mb="xl">
				<Box
					w={4}
					h={32}
					style={{
						borderRadius: 4,
						background:
							"linear-gradient(to bottom, #9b5de5, #7b2cbf)",
					}}
				/>

				<Title order={1} size="h2" fw={700} c="dark">
					Upcoming Events
				</Title>

				<Box
					style={{
						flex: 1,
						height: 1,
						background:
							"linear-gradient(to right, #ced4da, transparent)",
					}}
				/>
			</Flex>

			{props.upcomingEvents.length > 0 ? (
				<Grid gutter="lg">
					{props.upcomingEvents.map(event => (
						<Grid.Col
							key={event._id}
							span={{ base: 12, sm: 6, lg: 4 }}
							style={{ display: "flex" }}
						>
							<EventCard event={event} />
						</Grid.Col>
					))}
				</Grid>
			) : (
				<Paper
					p="xl"
					radius="xl"
					withBorder
					bg="var(--mantine-color-gray-light)"
					ta="center"
				>
					<Text c="dimmed" size="lg">
						No upcoming events at the moment.
					</Text>
					<Text c="dimmed" size="sm" mt="xs">
						Check back soon for new exciting events!
					</Text>
				</Paper>
			)}
		</Box>
	);
};
