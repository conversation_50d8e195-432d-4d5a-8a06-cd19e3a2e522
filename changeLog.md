# ChangeLog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/)

## Unreleased [1.0.7]

### Added [16-09-2025]

- **Video Re-upload:** Users can now re-upload videos if an admin has requested changes.
- **On Video Step** if user already have video show the video and (if profile status is requested changes also show reupload button)
- **Video Processing Feedback:** The UI now provides real-time feedback during video processing and prevents navigation until processing is complete.
- **Enhanced Video Status Tracking:** Implemented polling to track video transcription status from pending to completion.
- **Video Processing Failure:** In case of repeated video processing failures, the system now gracefully defaults to the user's last successfully processed video

## Fixed [16-09-2025]

- [#99](https://github.com/CQ-Dev-Team/360/issues/99) Show User name instead of "User" text
- [[#98](https://github.com/CQ-Dev-Team/360/issues/98)] Remove unnecessary things

### Fixed

- [#97](https://github.com/CQ-Dev-Team/360/issues/97) UI issue in Pending Profiles and Feedback page
- - ZIndex issue in unread count in pending profile and feedback page fixed
- [#96](https://github.com/CQ-Dev-Team/360/issues/96) Feedbacks user flow
- - Admin can only see (Admin, CommunityMember) users feedbacks and SuperAdmin can see all users feedbacks.
- [#93](https://github.com/CQ-Dev-Team/360/issues/93) About button missing if again onboarding
- - About Gang 360 Button is visible in two cases onBoarding and ChangesRequested
- [#94](https://github.com/CQ-Dev-Team/360/issues/94) Edit not work if again in onboarding
- - Edit is now working in three case onboaridng, pending, chnages requested.
- [#95](https://github.com/CQ-Dev-Team/360/issues/95) Search not working for full name
- - Now search is working fine with full name in global search, pending profile, and user listing, feedback listing, all feedbacks.
- [[#92](https://github.com/CQ-Dev-Team/360/issues/92)] corretly find feedback for unread
- [#91](https://github.com/CQ-Dev-Team/360/issues/91) no pointer cursor over check box to select it
- [#91](https://github.com/CQ-Dev-Team/360/issues/91) update onboarding tool tip (instead of "Form" write "Details")
- [#90](https://github.com/CQ-Dev-Team/360/issues/90) fix border remove issue on onboarding step
- [#89](https://github.com/CQ-Dev-Team/360/issues/89) update all modal (set trapFocus to false) so that its not autofocus on the Close button

## --Feedback Feature Start--

### Added

- Now for pending profile action status is visible in pending profile table
- Admins can send targeted feedback to users for specific profile sections (Basic Details, Early Life, Professional Life, Current Life).
- Users receive real-time notifications and can view feedback with read/unread status indicators.
- New feedback dashboard for admins includes feedback counts, searching, filtering by status, and get full history per user.
- Multi-step modal for sending feedback and components for viewing detailed feedback history.
- Bell icon notification system displays unread feedback indicators.
- Feedback archiving automatically occurs when a profile is approved.
- Added new profile status for "changes_requested" and updated related constants

### Improved

- Pending profile table now displays feedback status, unread count if any and onboarding progress.
- Added onboarding validation to prevent early profile review access and support 'resend to onboarding' after feedback.
- Profile section preview UI now includes edit icons and Paper-style wrapping for better clarity.
- Pagination is visible and consistent across tables and dashboards.

### Backend/API

- New endpoints:
  - `/api/feedbacks/send-feedback`
  - `/api/feedbacks/get-user-feedbacks/:userId`
  - `/api/feedbacks/notifications`
  - `/api/feedbacks/all-feedbacks`
  - `/api/feedbacks/read/:feedbackId`
- New `Feedback` model and references in `User` schema for feedback and resend to onboarding.

### Email

- Added `feedbackTemplate`: Users receive detailed feedback via email when an admin sends feedback.
- Updated email branding to 'Supermorpheus Team'.

## --Feedback Feature End--

## Released [1.0.6]

### Added: Descriptive text for user while waiting for Re-approval and new Filter in Users Page

- Unverified Icon for Unverified Users in the Users Table
- Added Tooltip for large First Name, Last Name or Email
- Your changes are currently visible only to you and admins. Once approved, they will be visible to everyone.
- Added filter for all, pending and approval(re-approved + approved)

### Renamed

- Pending Re-approval badge to Awaiting Re-approval

- Added new status: onboarding.

- Updated Profile and Pending Profile to use getProfileStatus() and show statuses with capitalize styling.

### Filtering & sorting

- Added sort order toggle (ascending/descending) with icons in Users and Pending Profiles.

- Added profile status filter including "Onboarding".

- Renamed title from Pending Approvals → Pending Profiles.

## Backend (server)

- Updated User model: profileStatus now includes "onboarding" (default).

- On final review request, status set to pending.

- getAllUsers: now restricted to approved/re-approved users only, requires admin check.

- getPendingApprovals: supports onboarding, pending, re-approved, and all; added sort order; improved query handling.

Migration

New migration 1.0.4: converts users with profileStatus: pending + onboardingStep < WAIT_FOR_APPROVAL → onboarding.

### Fixed

- Welcome modal for user(it is shown when the user logs in for the first time)
- Now Admin can create Admin users, also the admin users will be shown to the admin in user listing
- Added Tooltip for large First Name, Last Name or Email

- Descriptive text for user while waiting for Re-approval and new Filter in Users Page
- - Your changes are currently visible only to you and admins. Once approved, they will be visible to everyone.
- Add header logo in Navbar.

- [#86](https://github.com/CQ-Dev-Team/360/issues/86) Final step redirection
- Corrected icons for filters and Tabs persistance on refresh in pages

- - Added new icons in Filters
- - Fixed the issue of tabs not persisting after refresh in SearchedUser page and Profile page

- [[#79](https://github.com/CQ-Dev-Team/360/issues/79)] Approve modal search
- - Improved user display and search query handling

### Refactor

- Updated the logo in favicon
- Updated the email templates
- Updated the App title to `Gang 360`
- Changed the video prompts in onboarding steps and while recording video
- Add Pagination options to top and bottom of table
- By default 10 users per page in tables
- Pending Re-approval badge to Awaiting Re-approval

## Released

### Fixed: Filter issue in Pending Approval Page [#75](https://github.com/CQ-Dev-Team/360/issues/75)

- Adjusted column span for `No Users Found` message and reset page to 1 on profile status change

### Fixed: Open new tab when click on Empty link fields [[#73](https://github.com/CQ-Dev-Team/360/issues/73)]

- Ensured empty link fields display correctly in profile

### Feature

- Implemented a robust deep-diffing mechanism to accurately track additions, deletions, and modifications within arrays of objects (e.g., schools, jobs). This ensures that changes are correctly identified even if the order of items is altered.

### Fix

- Corrected the UI display for profile reviews to ensure that added and removed items in lists are highlighted consistently and accurately.

### Refactor

- Centralized the styling logic for displaying data differences (`getDiffStyle`) into a shared utility function, removing redundant code from multiple components.

### Chore

- Added a unique `_id` field to all sub-documents within the user data models (schools, universities, jobs, organizations) to improve data integrity and facilitate better tracking.
- Created a data migration script to retroactively add the new `_id` to all existing records in the database.

### Fixed

- ### Fixed getting undefined instead of life type [[#71](https://github.com/CQ-Dev-Team/360/issues/71)]

- ### Fixed: Open new tab when click on Empty link fields [[#73](https://github.com/CQ-Dev-Team/360/issues/73)]

- - Ensured empty link fields display correctly in profile  

- ### Improve [[#70]](https://github.com/CQ-Dev-Team/360/issues/70)

- - while creating new user change label of "Mobile" to something like "Mobile (with active WhatsApp)"

- - Selected badge background color to green from blue

- ### Fixed user can not see their own profile

- - update search route and backend this will common in search in pending profile

## [1.0.4]

### Added

- Early Life Sumamry and Professional Life Summary added in while Data is extracted from the video with same checks as in Current Life Summary.

- Add new field (pincode) in Basic Details.

### Updated

- Change CommunityMember to Community Member and same for others role in all places where it is displayed

- Update the video upload message for all three videos after video uploaded

- Updated email templates for welcoming user, processed videos and profile approved.

- Updated Approval Pending Message on Profile onboarding step

### Fixed

- Email suggestion dropdown style in Referer and Curator while approving user
-

## [1.0.3]

### Fixed

- Added validator package (isEmail) method in login page and create user form to validate email

### Added

- Introduced `PhoneInput` field in Create User form for capturing mobile numbers.
- Added validation logic using validator library (isMobilePhone) method to ensure valid international phone number format.
- Aligned PhoneInput disabled state styling with Mantine’s input design.

### Fixed: Image Scroll not working after one time [[#52](https://github.com/CQ-Dev-Team/360/issues/52)]

- Updated the handleSave function to check for image validation error

### Implement difference tracking for life data

- Added diff tracking functionality, allowing admin users to see changes made in early life, professional life, and current life data by users.
- Introduced new components: DiffCount, DiffList, JobDisplay, LifeTags, and RenderDiff to handle the display of changes.
- Updated the ProfileReview page to accommodate new updated data fields for early life, professional life, and current life.
- Modified the user controller to send updated life data instead of change flags.
- Enhanced deep-diff utility to support detailed change tracking and filtering of ignored keys.

### Fixed: Input field content size control [[#27](https://github.com/CQ-Dev-Team/360/issues/27)]

- Added mantine spoiler component for profile fields and replaced delete icon with IconTrash

### Fixed: Made the Tabs sticky in SearcherUser and also controlled input field size

- Added spoilers and made the tabs stick to top on scroll

## Released

## [1.0.2]

### Fixed: Smooth Scroll for Input Errors in Basic Details [[#52](https://github.com/CQ-Dev-Team/360/issues/52)]

- Now when there is an error in an input field in basic details, the user gets to know which input field is giving error due to smooth scroll.

### Fixed: Search on user listing and pending approvals [[#48](https://github.com/CQ-Dev-Team/360/issues/48)]

- Ensured search input trimming and implemented debounced search

### Fixed: isDirty form check on save in forms

- Added onBlur handlers on input fields

### Fixed: Border of Dropdowns [[#50](https://github.com/CQ-Dev-Team/360/issues/50)]

- Update gap size in search input groups for styling consistency

### Added: Login Assistance Modal to login page

- Added modal with admin email and phone

## Released

## [1.0.1]

## AppLayout Navigation

- **Added** `NavItemsDataType` with `navigation` and `divider` types.
- **Refactored** `navItems` to use `useMemo` instead of push-based logic.
- **Introduced** `showIf` boolean for conditional rendering.
- **Added** `"Admin Controls"` divider for non-community members.
- **Removed** `user?.canApproveUser` check (previously gated `Pending Profiles`) that is not needed.

### Updated Professional Life Video Instruction

- ### Before

- - Different jobs, roles, or positions you’ve held – including internships, employment, entrepreneurship, or freelancing.
- - Cover all career experiences except what you are doing right now.

- ### After

- - Talk about the different jobs or roles you’ve had in the past (like intern, employee, founder, owner, or freelancer).
- - Be sure to mention your titles too (for example: analyst, manager, director). Don’t include your current role—focus only on your past experiences.

### Update UI and OpenAI prompt for Professional Life Video

- Now Instead of seprate field for roles, we have a single field as tags in roles.
- Change the prompt for openAI to extract data from the video. (Added new Line in Prompt: When extracting roles, normalize them into valid professional job titles.Ensure roles sound like proper job designations (Analyst, Manager, Associate, Specialist, Executive, etc.) rather than just functional areas.)

## [1.0.0]

### Added

- Add admin panel functionality.
- - Implemented admin panel view for SuperAdmin and Admin users.
- - Added isAdminPanelUser field to User model and updated related routes and middleware.
- - Added checkAdminPanelView middleware to check if user is allowed to change admin panel view.
- - Created changeAdminPanelView controller to toggle admin panel access.
- - Added button in header to change admin panel view and switch back to onboarding view if user is not on onboarding step. conditionally show/hide based on user role.
- - Profile Life Data tabs are hidden if user user is on admin panel view.

### Added: Add video recording and instruction components with framer-motion integration

- Implemented VideoInstructionsModal and Instruction components for video guidance (make it reuseable).
- Created InstructionData to manage instructional content based on video type.
- Change file location of RecordVideoModal and VideoRecorder for video recording functionality.
- Integrated framer-motion for smooth transitions between recording states.
- Updated package.json and package-lock.json to include framer-motion dependency.

### Added : The recorded/uploaded video will be part of your profile and will be visible to other community members in video upload when video is selected / recorded

### Updated UI Message After Video Upload

- ### Before

- - Thanks for uploading your video We're hard at work processing them to create your personalized experience. This might take a few moments.
- - Once the video is processed and data is extracted, we will notify you via email.

- ### After

- - Thanks for uploading your video! We're processing it to create your personalized experience.
- - This usually takes 1–2 minutes. You’ll be automatically redirected to next stage once it’s ready, and we’ll also notify you by email.

### Fixed

### Updated Global Search for user

- Earlier user will search on the basis of first name and last name
- Now user can search on the basis of first name, middle name, last name, email, early life tags, professional life tags, current life tags
  
- Issue[[#45](https://github.com/CQ-Dev-Team/360/issues/45)] update file upload paths to support image uploads and migrate existing video paths

### Removed
