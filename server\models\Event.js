import mongoose from "mongoose";
import { rolesValuesNumToStr } from "../constants/index.js";

const commentSchema = new mongoose.Schema({
	userId: {
		type: mongoose.Schema.Types.ObjectId,
		ref: "User",
		required: true,
	},
	text: { type: String, required: true, trim: true },
	createdAt: { type: Date, default: Date.now },
});

const mediaSchema = new mongoose.Schema({
	key: { type: String, required: true }, // AWS S3 object key
	url: { type: String, required: true }, // S3 file URL or pre-signed
	type: { type: String, enum: ["image", "video"], required: true },
	uploadedBy: {
		type: mongoose.Schema.Types.ObjectId,
		ref: "User",
		required: true,
	},
	uploadedAt: { type: Date, default: Date.now },

	source: {
		type: String,
		enum: [
			rolesValuesNumToStr[1],
			rolesValuesNumToStr[2],
			rolesValuesNumToStr[3],
		],
		required: true,
	},

	// Only need if source is CommunityMember
	status: {
		type: String,
		enum: ["pending", "approved", "rejected"],
	},
	approvedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
});

const eventSchema = new mongoose.Schema({
	name: { type: String, required: true, trim: true },
	description: { type: String, required: true, trim: true },
	startDate: { type: Date, required: true },
	endDate: { type: Date, required: true },

	createdBy: {
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "User",
			required: true,
		},
		date: { type: Date, default: Date.now },
	},
	updatedBy: [
		{
			userId: {
				type: mongoose.Schema.Types.ObjectId,
				ref: "User",
				required: true,
			},
			date: { type: Date, default: Date.now },
		},
	],

	attendees: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],
	nonAttendees: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],

	creationMedia: [mediaSchema],

	eventMedia: [mediaSchema],
	comments: [commentSchema],
});

const Event = mongoose.model("Event", eventSchema);
export default Event;
